[gd_scene load_steps=5 format=3 uid="uid://cu6by5nkfuf3v"]

[ext_resource type="Texture2D" uid="uid://dgnp2kdplim20" path="res://asset/images/RAH_Game_Launcher logo.png" id="1_4emaf"]
[ext_resource type="Script" uid="uid://drrja7g7pfvfa" path="res://button_4.gd" id="1_uy6ji"]
[ext_resource type="Texture2D" uid="uid://dafwc664v70tb" path="res://asset/images/2gw3hS.jpg" id="2_cvfd6"]
[ext_resource type="Texture2D" uid="uid://0fuytutl4kv6" path="res://asset/images/this is a placeholder.jpg" id="2_vcerv"]

[node name="LAUNCHER" type="Control"]
layout_mode = 3
anchor_right = 1.0
anchor_bottom = 1.358
offset_right = 4.0
offset_bottom = -187.984
grow_horizontal = 2
grow_vertical = 2

[node name="background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = -3.0
offset_bottom = -45.0
grow_horizontal = 2
grow_vertical = 2

[node name="MarginContainer" type="MarginContainer" parent="background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 32
theme_override_constants/margin_top = 15
theme_override_constants/margin_right = 32
theme_override_constants/margin_bottom = 16

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer"]
layout_mode = 2

[node name="HBoxContainer2" type="HBoxContainer" parent="background/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="Button4" type="Button" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "⚙️"
script = ExtResource("1_uy6ji")

[node name="Button5" type="Button" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "🔔"

[node name="Label" type="Label" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Button6" type="Button" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "wishlist"

[node name="Button7" type="Button" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
text = "carts"

[node name="Button8" type="Button" parent="background/MarginContainer/VBoxContainer/HBoxContainer2"]
layout_mode = 2
text = "🖼️"

[node name="HBoxContainer" type="HBoxContainer" parent="background/MarginContainer/VBoxContainer"]
layout_mode = 2

[node name="logo" type="TextureRect" parent="background/MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
size_flags_stretch_ratio = 0.0
texture = ExtResource("1_4emaf")

[node name="Label" type="Label" parent="background/MarginContainer/VBoxContainer/HBoxContainer"]
layout_mode = 2

[node name="Panel" type="Panel" parent="background/MarginContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MarginContainer" type="MarginContainer" parent="background/MarginContainer/VBoxContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_right = 24.0
offset_bottom = 213.0
grow_horizontal = 2
grow_vertical = 2

[node name="HBoxContainer" type="HBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer2" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_cvfd6")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer9" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer2"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer3" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer3"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer3"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer4" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer4"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer4"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer5" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer5"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer5"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer6" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer6"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer6"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer8" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer8"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer8"]
layout_mode = 2
size_flags_vertical = 3

[node name="VBoxContainer7" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="TextureRect2" type="TextureRect" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer7"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
texture = ExtResource("2_vcerv")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="background/MarginContainer/VBoxContainer/Panel/MarginContainer/HBoxContainer/VBoxContainer7"]
layout_mode = 2
size_flags_vertical = 3
