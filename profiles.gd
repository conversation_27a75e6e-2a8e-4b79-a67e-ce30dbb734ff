extends Button

# Multi-Platform Login System - All-in-one implementation
# Supports Proton, Steam, and Discord authentication

# Login providers
enum LoginProvider {
	NONE,
	PROTON,
	STEAM,
	DISCORD
}

# Authentication states
enum AuthState {
	LOGGED_OUT,
	LOGGING_IN,
	LOGGED_IN,
	ERROR
}

# Profile data structure
var current_profile = {
	"provider": LoginProvider.NONE,
	"user_id": "",
	"username": "",
	"display_name": "",
	"avatar_url": "",
	"email": "",
	"auth_token": "",
	"refresh_token": "",
	"login_time": "",
	"last_active": ""
}

# System configuration
const PROFILES_FILE = "user://profiles.json"
const AUTH_CACHE_FILE = "user://auth_cache.json"

# API endpoints and configuration
const STEAM_API_KEY = "YOUR_STEAM_API_KEY"  # Replace with actual key
const DISCORD_CLIENT_ID = "YOUR_DISCORD_CLIENT_ID"  # Replace with actual ID
const DISCORD_CLIENT_SECRET = "YOUR_DISCORD_CLIENT_SECRET"  # Replace with actual secret
const PROTON_API_BASE = "https://api.protonmail.com"  # Proton API endpoint

# UI Components
var login_window: Window
var login_container: Control
var current_auth_state: AuthState = AuthState.LOGGED_OUT
var selected_provider: LoginProvider = LoginProvider.NONE

# HTTP requests
var http_request: HTTPRequest
var auth_http_request: HTTPRequest

# Signals
signal login_successful(provider: LoginProvider, profile: Dictionary)
signal login_failed(provider: LoginProvider, error: String)
signal logout_completed(provider: LoginProvider)
signal profile_updated(profile: Dictionary)

func _ready():
	# Connect button press to open login window
	pressed.connect(_on_button_pressed)

	# Set up HTTP requests
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_http_request_completed)

	auth_http_request = HTTPRequest.new()
	add_child(auth_http_request)
	auth_http_request.request_completed.connect(_on_auth_request_completed)

	# Load saved profile
	load_profile()

	# Update button text
	update_button_text()

func _on_button_pressed():
	"""Open login window when button is clicked"""
	if current_auth_state == AuthState.LOGGED_IN:
		show_profile_window()
	else:
		show_login_window()

# ============================================================================
# PROFILE MANAGEMENT
# ============================================================================

func save_profile():
	"""Save current profile to file"""
	var save_data = {
		"profile": current_profile,
		"auth_state": current_auth_state,
		"last_save": Time.get_datetime_string_from_system()
	}

	var file = FileAccess.open(PROFILES_FILE, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(save_data)
		file.store_string(json_string)
		file.close()
		print("Profile saved successfully")

func load_profile():
	"""Load profile from file"""
	if FileAccess.file_exists(PROFILES_FILE):
		var file = FileAccess.open(PROFILES_FILE, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()

			var json = JSON.new()
			var parse_result = json.parse(json_string)

			if parse_result == OK:
				var data = json.data
				if data.has("profile"):
					current_profile = data.profile
				if data.has("auth_state"):
					current_auth_state = data.auth_state

				# Validate saved session
				validate_saved_session()

func validate_saved_session():
	"""Validate if saved session is still valid"""
	if current_auth_state == AuthState.LOGGED_IN and not current_profile.auth_token.is_empty():
		# Check if token is still valid based on provider
		match current_profile.provider:
			LoginProvider.STEAM:
				validate_steam_session()
			LoginProvider.DISCORD:
				validate_discord_session()
			LoginProvider.PROTON:
				validate_proton_session()
	else:
		current_auth_state = AuthState.LOGGED_OUT

func clear_profile():
	"""Clear current profile data"""
	current_profile = {
		"provider": LoginProvider.NONE,
		"user_id": "",
		"username": "",
		"display_name": "",
		"avatar_url": "",
		"email": "",
		"auth_token": "",
		"refresh_token": "",
		"login_time": "",
		"last_active": ""
	}
	current_auth_state = AuthState.LOGGED_OUT
	save_profile()
	update_button_text()

func update_button_text():
	"""Update button text based on login state"""
	match current_auth_state:
		AuthState.LOGGED_OUT:
			text = "Login"
		AuthState.LOGGING_IN:
			text = "Logging in..."
		AuthState.LOGGED_IN:
			var provider_name = get_provider_name(current_profile.provider)
			text = current_profile.display_name + " (" + provider_name + ")"
		AuthState.ERROR:
			text = "Login Error"

# ============================================================================
# STEAM AUTHENTICATION
# ============================================================================

func login_steam():
	"""Initiate Steam login process"""
	current_auth_state = AuthState.LOGGING_IN
	update_button_text()

	# Steam uses OpenID authentication
	# In a real implementation, you'd open Steam's OpenID URL
	var steam_openid_url = "https://steamcommunity.com/openid/login"
	var return_url = "http://localhost:8080/auth/steam/callback"  # Your callback URL

	var params = {
		"openid.ns": "http://specs.openid.net/auth/2.0",
		"openid.mode": "checkid_setup",
		"openid.return_to": return_url,
		"openid.realm": "http://localhost:8080",
		"openid.identity": "http://specs.openid.net/auth/2.0/identifier_select",
		"openid.claimed_id": "http://specs.openid.net/auth/2.0/identifier_select"
	}

	var url_params = ""
	for key in params.keys():
		if url_params != "":
			url_params += "&"
		url_params += key + "=" + params[key].uri_encode()

	var full_url = steam_openid_url + "?" + url_params

	# Open Steam login in browser
	OS.shell_open(full_url)

	# For demo purposes, simulate successful login after delay
	await get_tree().create_timer(3.0).timeout
	simulate_steam_login_success()

func simulate_steam_login_success():
	"""Simulate successful Steam login (for demo)"""
	current_profile.provider = LoginProvider.STEAM
	current_profile.user_id = "76561198000000000"  # Example Steam ID
	current_profile.username = "steam_user"
	current_profile.display_name = "Steam User"
	current_profile.avatar_url = "https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/default.jpg"
	current_profile.auth_token = "steam_auth_token_" + str(Time.get_unix_time_from_system())
	current_profile.login_time = Time.get_datetime_string_from_system()
	current_profile.last_active = current_profile.login_time

	current_auth_state = AuthState.LOGGED_IN
	save_profile()
	update_button_text()
	login_successful.emit(LoginProvider.STEAM, current_profile)

	print("Steam login successful: ", current_profile.display_name)

func validate_steam_session():
	"""Validate Steam session token"""
	if current_profile.auth_token.is_empty():
		current_auth_state = AuthState.LOGGED_OUT
		return

	# In real implementation, validate with Steam API
	# For demo, assume valid if token exists and is recent
	var login_time = Time.get_datetime_dict_from_string(current_profile.login_time)
	var current_time = Time.get_datetime_dict_from_system()

	# Simple validation: token valid for 24 hours
	# Parse the login time string to get unix timestamp
	var now_time = Time.get_unix_time_from_system()
	var login_unix_time = parse_datetime_to_unix(current_profile.login_time)
	var time_diff = now_time - login_unix_time
	if time_diff > 86400:  # 24 hours
		current_auth_state = AuthState.LOGGED_OUT
		print("Steam session expired")

func get_steam_user_info(steam_id: String):
	"""Get Steam user information"""
	var url = "https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v0002/"
	url += "?key=" + STEAM_API_KEY + "&steamids=" + steam_id

	http_request.request(url)

# ============================================================================
# DISCORD AUTHENTICATION
# ============================================================================

func login_discord():
	"""Initiate Discord login process"""
	current_auth_state = AuthState.LOGGING_IN
	update_button_text()

	# Discord OAuth2 flow
	var discord_auth_url = "https://discord.com/api/oauth2/authorize"
	var redirect_uri = "http://localhost:8080/auth/discord/callback"  # Your callback URL
	var scope = "identify email"

	var params = {
		"client_id": DISCORD_CLIENT_ID,
		"redirect_uri": redirect_uri,
		"response_type": "code",
		"scope": scope
	}

	var url_params = ""
	for key in params.keys():
		if url_params != "":
			url_params += "&"
		url_params += key + "=" + params[key].uri_encode()

	var full_url = discord_auth_url + "?" + url_params

	# Open Discord login in browser
	OS.shell_open(full_url)

	# For demo purposes, simulate successful login after delay
	await get_tree().create_timer(3.0).timeout
	simulate_discord_login_success()

func simulate_discord_login_success():
	"""Simulate successful Discord login (for demo)"""
	current_profile.provider = LoginProvider.DISCORD
	current_profile.user_id = "123456789012345678"  # Example Discord ID
	current_profile.username = "discord_user#1234"
	current_profile.display_name = "Discord User"
	current_profile.email = "<EMAIL>"
	current_profile.avatar_url = "https://cdn.discordapp.com/avatars/123456789012345678/avatar.png"
	current_profile.auth_token = "discord_auth_token_" + str(Time.get_unix_time_from_system())
	current_profile.refresh_token = "discord_refresh_token_" + str(Time.get_unix_time_from_system())
	current_profile.login_time = Time.get_datetime_string_from_system()
	current_profile.last_active = current_profile.login_time

	current_auth_state = AuthState.LOGGED_IN
	save_profile()
	update_button_text()
	login_successful.emit(LoginProvider.DISCORD, current_profile)

	print("Discord login successful: ", current_profile.display_name)

func validate_discord_session():
	"""Validate Discord session token"""
	if current_profile.auth_token.is_empty():
		current_auth_state = AuthState.LOGGED_OUT
		return

	# Check token validity with Discord API
	var url = "https://discord.com/api/users/@me"
	var headers = ["Authorization: Bearer " + current_profile.auth_token]

	auth_http_request.request(url, headers)

func refresh_discord_token():
	"""Refresh Discord access token"""
	if current_profile.refresh_token.is_empty():
		current_auth_state = AuthState.LOGGED_OUT
		return

	var url = "https://discord.com/api/oauth2/token"
	var headers = ["Content-Type: application/x-www-form-urlencoded"]

	var body = "grant_type=refresh_token"
	body += "&refresh_token=" + current_profile.refresh_token
	body += "&client_id=" + DISCORD_CLIENT_ID
	body += "&client_secret=" + DISCORD_CLIENT_SECRET

	auth_http_request.request(url, headers, HTTPClient.METHOD_POST, body)

func get_discord_user_info():
	"""Get Discord user information"""
	var url = "https://discord.com/api/users/@me"
	var headers = ["Authorization: Bearer " + current_profile.auth_token]

	http_request.request(url, headers)

# ============================================================================
# PROTON AUTHENTICATION
# ============================================================================

func login_proton():
	"""Initiate Proton login process"""
	current_auth_state = AuthState.LOGGING_IN
	update_button_text()

	# Show Proton login form
	show_proton_login_form()

func show_proton_login_form():
	"""Show Proton email/password login form"""
	var form_window = Window.new()
	form_window.title = "Proton Login"
	form_window.size = Vector2i(400, 300)
	form_window.position = Vector2i(300, 300)
	get_tree().root.add_child(form_window)

	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("separation", 15)
	form_window.add_child(container)

	# Title
	var title = Label.new()
	title.text = "Login to Proton"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 18)
	container.add_child(title)

	# Email field
	var email_label = Label.new()
	email_label.text = "Email:"
	container.add_child(email_label)

	var email_input = LineEdit.new()
	email_input.placeholder_text = "Enter your Proton email"
	container.add_child(email_input)

	# Password field
	var password_label = Label.new()
	password_label.text = "Password:"
	container.add_child(password_label)

	var password_input = LineEdit.new()
	password_input.placeholder_text = "Enter your password"
	password_input.secret = true
	container.add_child(password_input)

	# Buttons
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER

	var login_button = Button.new()
	login_button.text = "Login"
	login_button.pressed.connect(_on_proton_login_submit.bind(email_input, password_input, form_window))
	button_container.add_child(login_button)

	var cancel_button = Button.new()
	cancel_button.text = "Cancel"
	cancel_button.pressed.connect(_on_proton_login_cancel.bind(form_window))
	button_container.add_child(cancel_button)

	container.add_child(button_container)

	form_window.show()

func _on_proton_login_submit(email_input: LineEdit, password_input: LineEdit, form_window: Window):
	"""Handle Proton login form submission"""
	var email = email_input.text
	var password = password_input.text

	if email.is_empty() or password.is_empty():
		show_error_message("Please enter both email and password")
		return

	form_window.queue_free()

	# Authenticate with Proton
	authenticate_proton(email, password)

func _on_proton_login_cancel(form_window: Window):
	"""Handle Proton login cancellation"""
	form_window.queue_free()
	current_auth_state = AuthState.LOGGED_OUT
	update_button_text()

func authenticate_proton(email: String, password: String):
	"""Authenticate with Proton API"""
	# In real implementation, this would make actual API calls to Proton
	# For demo purposes, simulate authentication

	print("Authenticating with Proton: ", email)

	# Simulate API delay
	await get_tree().create_timer(2.0).timeout

	# Simulate successful authentication
	if email.contains("@") and password.length() >= 6:
		simulate_proton_login_success(email)
	else:
		proton_login_failed("Invalid credentials")

func simulate_proton_login_success(email: String):
	"""Simulate successful Proton login"""
	current_profile.provider = LoginProvider.PROTON
	current_profile.user_id = "proton_" + str(email.hash())
	current_profile.username = email.split("@")[0]
	current_profile.display_name = email.split("@")[0].capitalize()
	current_profile.email = email
	current_profile.avatar_url = ""  # Proton doesn't typically have avatars
	current_profile.auth_token = "proton_auth_token_" + str(Time.get_unix_time_from_system())
	current_profile.login_time = Time.get_datetime_string_from_system()
	current_profile.last_active = current_profile.login_time

	current_auth_state = AuthState.LOGGED_IN
	save_profile()
	update_button_text()
	login_successful.emit(LoginProvider.PROTON, current_profile)

	print("Proton login successful: ", current_profile.display_name)

func proton_login_failed(error: String):
	"""Handle Proton login failure"""
	current_auth_state = AuthState.ERROR
	update_button_text()
	login_failed.emit(LoginProvider.PROTON, error)
	show_error_message("Proton login failed: " + error)

func validate_proton_session():
	"""Validate Proton session"""
	if current_profile.auth_token.is_empty():
		current_auth_state = AuthState.LOGGED_OUT
		return

	# In real implementation, validate with Proton API
	# For demo, assume valid if token exists and is recent
	var current_time = Time.get_unix_time_from_system()
	var login_unix_time = parse_datetime_to_unix(current_profile.login_time)
	var time_diff = current_time - login_unix_time
	if time_diff > 86400:  # 24 hours
		current_auth_state = AuthState.LOGGED_OUT
		print("Proton session expired")

# ============================================================================
# UI SYSTEM
# ============================================================================

func show_login_window():
	"""Show the login provider selection window"""
	if login_window:
		login_window.queue_free()

	login_window = Window.new()
	login_window.title = "Choose Login Method"
	login_window.size = Vector2i(400, 350)
	login_window.position = Vector2i(250, 250)
	login_window.close_requested.connect(_on_login_window_closed)
	get_tree().root.add_child(login_window)

	create_login_ui()
	login_window.show()

func create_login_ui():
	"""Create the login selection UI"""
	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("separation", 20)
	login_window.add_child(container)

	# Title
	var title = Label.new()
	title.text = "Select Login Provider"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 20)
	container.add_child(title)

	# Provider buttons
	var providers_container = VBoxContainer.new()
	providers_container.add_theme_constant_override("separation", 15)

	# Steam button
	var steam_button = create_provider_button("Steam", "Login with Steam", Color.BLUE)
	steam_button.pressed.connect(_on_provider_selected.bind(LoginProvider.STEAM))
	providers_container.add_child(steam_button)

	# Discord button
	var discord_button = create_provider_button("Discord", "Login with Discord", Color.PURPLE)
	discord_button.pressed.connect(_on_provider_selected.bind(LoginProvider.DISCORD))
	providers_container.add_child(discord_button)

	# Proton button
	var proton_button = create_provider_button("Proton", "Login with Proton", Color.GREEN)
	proton_button.pressed.connect(_on_provider_selected.bind(LoginProvider.PROTON))
	providers_container.add_child(proton_button)

	container.add_child(providers_container)

	# Cancel button
	var cancel_button = Button.new()
	cancel_button.text = "Cancel"
	cancel_button.pressed.connect(_on_login_window_closed)
	container.add_child(cancel_button)

func create_provider_button(provider_name: String, description: String, color: Color) -> Button:
	"""Create a styled provider button"""
	var button = Button.new()
	button.text = provider_name
	button.custom_minimum_size = Vector2(300, 60)

	# Style the button
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = color
	style_box.corner_radius_top_left = 10
	style_box.corner_radius_top_right = 10
	style_box.corner_radius_bottom_left = 10
	style_box.corner_radius_bottom_right = 10
	button.add_theme_stylebox_override("normal", style_box)

	var hover_style = style_box.duplicate()
	hover_style.bg_color = color.lightened(0.2)
	button.add_theme_stylebox_override("hover", hover_style)

	return button

func show_profile_window():
	"""Show the logged-in user profile window"""
	if login_window:
		login_window.queue_free()

	login_window = Window.new()
	login_window.title = "User Profile"
	login_window.size = Vector2i(500, 400)
	login_window.position = Vector2i(250, 250)
	login_window.close_requested.connect(_on_login_window_closed)
	get_tree().root.add_child(login_window)

	create_profile_ui()
	login_window.show()

func create_profile_ui():
	"""Create the profile display UI"""
	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("separation", 15)
	login_window.add_child(container)

	# Header
	var header = HBoxContainer.new()

	# Avatar placeholder
	var avatar_container = PanelContainer.new()
	avatar_container.custom_minimum_size = Vector2(80, 80)
	var avatar_label = Label.new()
	avatar_label.text = current_profile.display_name.substr(0, 1).to_upper()
	avatar_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	avatar_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
	avatar_label.add_theme_font_size_override("font_size", 32)
	avatar_container.add_child(avatar_label)
	header.add_child(avatar_container)

	# User info
	var info_container = VBoxContainer.new()
	info_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL

	var name_label = Label.new()
	name_label.text = current_profile.display_name
	name_label.add_theme_font_size_override("font_size", 18)
	info_container.add_child(name_label)

	var provider_label = Label.new()
	provider_label.text = "Logged in via " + get_provider_name(current_profile.provider)
	provider_label.add_theme_color_override("font_color", Color.GRAY)
	info_container.add_child(provider_label)

	if not current_profile.email.is_empty():
		var email_label = Label.new()
		email_label.text = current_profile.email
		email_label.add_theme_color_override("font_color", Color.GRAY)
		info_container.add_child(email_label)

	header.add_child(info_container)
	container.add_child(header)

	# Profile details
	var details_container = VBoxContainer.new()
	details_container.add_theme_constant_override("separation", 10)

	add_profile_detail(details_container, "User ID:", current_profile.user_id)
	add_profile_detail(details_container, "Username:", current_profile.username)
	add_profile_detail(details_container, "Login Time:", current_profile.login_time)
	add_profile_detail(details_container, "Last Active:", current_profile.last_active)

	container.add_child(details_container)

	# Action buttons
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	button_container.add_theme_constant_override("separation", 20)

	var refresh_button = Button.new()
	refresh_button.text = "Refresh Profile"
	refresh_button.pressed.connect(_on_refresh_profile)
	button_container.add_child(refresh_button)

	var logout_button = Button.new()
	logout_button.text = "Logout"
	logout_button.pressed.connect(_on_logout_pressed)
	button_container.add_child(logout_button)

	var close_button = Button.new()
	close_button.text = "Close"
	close_button.pressed.connect(_on_login_window_closed)
	button_container.add_child(close_button)

	container.add_child(button_container)

func add_profile_detail(container: VBoxContainer, label: String, value: String):
	"""Add a profile detail row"""
	var detail_container = HBoxContainer.new()

	var label_control = Label.new()
	label_control.text = label
	label_control.custom_minimum_size = Vector2(120, 0)
	label_control.add_theme_font_size_override("font_size", 12)
	detail_container.add_child(label_control)

	var value_control = Label.new()
	value_control.text = value if not value.is_empty() else "N/A"
	value_control.add_theme_font_size_override("font_size", 12)
	value_control.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	detail_container.add_child(value_control)

	container.add_child(detail_container)

func show_error_message(message: String):
	"""Show an error message dialog"""
	var error_window = Window.new()
	error_window.title = "Error"
	error_window.size = Vector2i(300, 150)
	error_window.position = Vector2i(350, 350)
	get_tree().root.add_child(error_window)

	var container = VBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	container.add_theme_constant_override("separation", 15)
	error_window.add_child(container)

	var message_label = Label.new()
	message_label.text = message
	message_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	message_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	container.add_child(message_label)

	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.pressed.connect(error_window.queue_free)
	container.add_child(ok_button)

	error_window.show()

# ============================================================================
# EVENT HANDLERS
# ============================================================================

func _on_provider_selected(provider: LoginProvider):
	"""Handle provider selection"""
	selected_provider = provider

	if login_window:
		login_window.queue_free()
		login_window = null

	match provider:
		LoginProvider.STEAM:
			login_steam()
		LoginProvider.DISCORD:
			login_discord()
		LoginProvider.PROTON:
			login_proton()

func _on_login_window_closed():
	"""Handle login window close"""
	if login_window:
		login_window.queue_free()
		login_window = null

func _on_refresh_profile():
	"""Handle profile refresh"""
	current_profile.last_active = Time.get_datetime_string_from_system()
	save_profile()

	# Refresh profile data from provider
	match current_profile.provider:
		LoginProvider.STEAM:
			get_steam_user_info(current_profile.user_id)
		LoginProvider.DISCORD:
			get_discord_user_info()
		LoginProvider.PROTON:
			# Proton doesn't have additional profile data to fetch
			pass

	# Refresh UI
	if login_window:
		login_window.queue_free()
		show_profile_window()

func _on_logout_pressed():
	"""Handle logout"""
	var provider = current_profile.provider
	clear_profile()
	logout_completed.emit(provider)

	if login_window:
		login_window.queue_free()
		login_window = null

	print("Logged out from ", get_provider_name(provider))

func _on_http_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""Handle HTTP request completion"""
	if response_code == 200:
		var response_text = body.get_string_from_utf8()
		var json = JSON.new()
		var parse_result = json.parse(response_text)

		if parse_result == OK:
			var data = json.data
			process_api_response(data)
	else:
		print("HTTP request failed with code: ", response_code)

func _on_auth_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""Handle authentication request completion"""
	if response_code == 200:
		print("Authentication validated successfully")
	else:
		print("Authentication validation failed: ", response_code)
		current_auth_state = AuthState.LOGGED_OUT
		update_button_text()

func process_api_response(data: Dictionary):
	"""Process API response data"""
	match current_profile.provider:
		LoginProvider.STEAM:
			if data.has("response") and data.response.has("players"):
				var players = data.response.players
				if players.size() > 0:
					var player = players[0]
					current_profile.display_name = player.get("personaname", current_profile.display_name)
					current_profile.avatar_url = player.get("avatarfull", current_profile.avatar_url)

		LoginProvider.DISCORD:
			current_profile.username = data.get("username", current_profile.username) + "#" + data.get("discriminator", "0000")
			current_profile.display_name = data.get("global_name", current_profile.display_name)
			current_profile.email = data.get("email", current_profile.email)
			if data.has("avatar") and data.avatar:
				current_profile.avatar_url = "https://cdn.discordapp.com/avatars/" + current_profile.user_id + "/" + data.avatar + ".png"

	save_profile()
	profile_updated.emit(current_profile)

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

func get_provider_name(provider: LoginProvider) -> String:
	"""Get human-readable provider name"""
	match provider:
		LoginProvider.STEAM:
			return "Steam"
		LoginProvider.DISCORD:
			return "Discord"
		LoginProvider.PROTON:
			return "Proton"
		_:
			return "Unknown"

func is_logged_in() -> bool:
	"""Check if user is currently logged in"""
	return current_auth_state == AuthState.LOGGED_IN

func get_current_provider() -> LoginProvider:
	"""Get current login provider"""
	return current_profile.provider

func get_user_id() -> String:
	"""Get current user ID"""
	return current_profile.user_id

func get_username() -> String:
	"""Get current username"""
	return current_profile.username

func get_display_name() -> String:
	"""Get current display name"""
	return current_profile.display_name

func get_email() -> String:
	"""Get current email"""
	return current_profile.email

func get_avatar_url() -> String:
	"""Get current avatar URL"""
	return current_profile.avatar_url

func get_auth_token() -> String:
	"""Get current auth token"""
	return current_profile.auth_token

func parse_datetime_to_unix(datetime_string: String) -> int:
	"""Parse datetime string to unix timestamp"""
	if datetime_string.is_empty():
		return 0

	# Try to parse the datetime string
	# Format: "YYYY-MM-DD HH:MM:SS"
	var parts = datetime_string.split(" ")
	if parts.size() != 2:
		return int(Time.get_unix_time_from_system())  # Fallback to current time

	var date_parts = parts[0].split("-")
	var time_parts = parts[1].split(":")

	if date_parts.size() != 3 or time_parts.size() != 3:
		return int(Time.get_unix_time_from_system())  # Fallback to current time

	var datetime_dict = {
		"year": int(date_parts[0]),
		"month": int(date_parts[1]),
		"day": int(date_parts[2]),
		"hour": int(time_parts[0]),
		"minute": int(time_parts[1]),
		"second": int(time_parts[2])
	}

	return Time.get_unix_time_from_datetime_dict(datetime_dict)

# ============================================================================
# PUBLIC API FUNCTIONS
# ============================================================================

func force_logout():
	"""Force logout regardless of current state"""
	var provider = current_profile.provider
	clear_profile()
	logout_completed.emit(provider)

func switch_provider(new_provider: LoginProvider):
	"""Switch to a different login provider"""
	if is_logged_in():
		force_logout()

	selected_provider = new_provider
	_on_provider_selected(new_provider)

func get_profile_summary() -> String:
	"""Get a text summary of current profile"""
	if not is_logged_in():
		return "Not logged in"

	var summary = "Profile Summary:\n"
	summary += "Provider: %s\n" % get_provider_name(current_profile.provider)
	summary += "Display Name: %s\n" % current_profile.display_name
	summary += "Username: %s\n" % current_profile.username
	summary += "User ID: %s\n" % current_profile.user_id
	if not current_profile.email.is_empty():
		summary += "Email: %s\n" % current_profile.email
	summary += "Login Time: %s\n" % current_profile.login_time
	summary += "Last Active: %s" % current_profile.last_active
	return summary

func export_profile() -> Dictionary:
	"""Export current profile data"""
	return current_profile.duplicate(true)

func import_profile(profile_data: Dictionary) -> bool:
	"""Import profile data"""
	if profile_data.has("provider") and profile_data.has("user_id"):
		current_profile = profile_data.duplicate(true)
		current_auth_state = AuthState.LOGGED_IN
		save_profile()
		update_button_text()
		return true
	return false

func validate_current_session():
	"""Manually validate current session"""
	if is_logged_in():
		validate_saved_session()
		update_button_text()

# ============================================================================
# CLEANUP
# ============================================================================

func _exit_tree():
	"""Clean up when node is removed"""
	save_profile()

func _notification(what):
	"""Handle engine notifications"""
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		save_profile()
