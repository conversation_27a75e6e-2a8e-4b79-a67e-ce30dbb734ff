extends Button

# Itch.io Notification System - All-in-one implementation
# Monitors itch.io pages for development logs and posts

# Configuration
const ITCH_IO_BASE_URL = "https://zwute-studio.itch.io"
const SPECIFIC_GAME_URL = "https://zwute-studio.itch.io/visual-novel-netcode-the-protogen-and-more"
const CHECK_INTERVAL = 300.0  # Check every 5 minutes (300 seconds)
const NOTIFICATIONS_FILE = "user://notifications.json"
const MAX_NOTIFICATIONS = 50  # Maximum notifications to keep

# Notification data structure
var notifications = []
var known_posts = {}  # Track known posts to avoid duplicates
var is_monitoring = false
var check_timer: Timer
var http_request: HTTPRequest

# UI Components
var notification_window: Window
var notification_list: VBoxContainer
var notification_count_label: Label

# Signals
signal new_notification(title: String, content: String, url: String)
signal notification_clicked(url: String)

# Notification structure:
# {
#   "id": "unique_id",
#   "title": "Post Title",
#   "content": "Post content preview",
#   "url": "Full URL to post",
#   "timestamp": "ISO timestamp",
#   "read": false,
#   "type": "devlog" or "post"
# }

func _ready():
	# Connect button press to toggle notification window
	pressed.connect(_on_button_pressed)

	# Load existing notifications
	load_notifications()

	# Set up HTTP request
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_http_request_completed)

	# Set up timer for periodic checks
	check_timer = Timer.new()
	check_timer.wait_time = CHECK_INTERVAL
	check_timer.timeout.connect(_check_for_updates)
	check_timer.autostart = false
	add_child(check_timer)

	# Update button text with notification count
	update_button_text()

	# Start monitoring
	start_monitoring()

func _on_button_pressed():
	"""Toggle notification window when button is clicked"""
	if notification_window and notification_window.visible:
		close_notification_window()
	else:
		open_notification_window()

# ============================================================================
# MONITORING SYSTEM
# ============================================================================

func start_monitoring():
	"""Start monitoring itch.io for updates"""
	if not is_monitoring:
		is_monitoring = true
		check_timer.start()
		_check_for_updates()  # Initial check
		print("Started monitoring itch.io for updates")

func stop_monitoring():
	"""Stop monitoring itch.io"""
	if is_monitoring:
		is_monitoring = false
		check_timer.stop()
		print("Stopped monitoring itch.io")

func _check_for_updates():
	"""Check for new posts and development logs"""
	if not is_monitoring:
		return

	print("Checking for itch.io updates...")

	# Check the main studio page first
	check_url(ITCH_IO_BASE_URL)

func check_url(url: String):
	"""Make HTTP request to check a specific URL"""
	var headers = [
		"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
	]

	var error = http_request.request(url, headers)
	if error != OK:
		print("Error making HTTP request: ", error)

func _on_http_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""Handle HTTP request completion"""
	if response_code == 200:
		var html_content = body.get_string_from_utf8()
		parse_itch_page(html_content)
	else:
		print("HTTP request failed with code: ", response_code)

# ============================================================================
# HTML PARSING SYSTEM
# ============================================================================

func parse_itch_page(html_content: String):
	"""Parse itch.io page HTML to find development logs and posts"""
	var new_posts_found = 0

	# Look for development log entries
	new_posts_found += parse_devlogs(html_content)

	# Look for general posts/updates
	new_posts_found += parse_posts(html_content)

	if new_posts_found > 0:
		print("Found ", new_posts_found, " new posts")
		save_notifications()
		update_button_text()

		# Update notification window if it's open
		if notification_window and notification_window.visible:
			refresh_notification_list()

func parse_devlogs(html_content: String) -> int:
	"""Parse development logs from HTML content"""
	var new_posts = 0

	# Look for devlog patterns in HTML
	# Itch.io typically uses specific classes for devlog entries
	var devlog_patterns = [
		'class="devlog_post"',
		'class="post_content"',
		'data-label="Development log"',
		'class="game_update"'
	]

	for pattern in devlog_patterns:
		var start_pos = 0
		while true:
			var pos = html_content.find(pattern, start_pos)
			if pos == -1:
				break

			# Extract post information around this position
			var post_data = extract_post_data(html_content, pos, "devlog")
			if post_data and not is_known_post(post_data.id):
				add_notification(post_data)
				new_posts += 1

			start_pos = pos + pattern.length()

	return new_posts

func parse_posts(html_content: String) -> int:
	"""Parse general posts from HTML content"""
	var new_posts = 0

	# Look for general post patterns
	var post_patterns = [
		'class="post"',
		'class="update"',
		'class="announcement"',
		'class="news_item"'
	]

	for pattern in post_patterns:
		var start_pos = 0
		while true:
			var pos = html_content.find(pattern, start_pos)
			if pos == -1:
				break

			var post_data = extract_post_data(html_content, pos, "post")
			if post_data and not is_known_post(post_data.id):
				add_notification(post_data)
				new_posts += 1

			start_pos = pos + pattern.length()

	return new_posts

func extract_post_data(html_content: String, start_pos: int, post_type: String) -> Dictionary:
	"""Extract post data from HTML around a specific position"""
	# Find the containing element (usually a div or article)
	var element_start = find_element_start(html_content, start_pos)
	var element_end = find_element_end(html_content, element_start)

	if element_start == -1 or element_end == -1:
		return {}

	var element_html = html_content.substr(element_start, element_end - element_start)

	# Extract title
	var title = extract_title(element_html)
	if title.is_empty():
		return {}

	# Extract content preview
	var content = extract_content_preview(element_html)

	# Extract URL
	var url = extract_post_url(element_html)

	# Generate unique ID
	var post_id = generate_post_id(title, url)

	return {
		"id": post_id,
		"title": title,
		"content": content,
		"url": url if not url.is_empty() else ITCH_IO_BASE_URL,
		"timestamp": Time.get_datetime_string_from_system(),
		"read": false,
		"type": post_type
	}

func find_element_start(html: String, pos: int) -> int:
	"""Find the start of the HTML element containing the position"""
	var search_pos = pos
	var depth = 0

	# Search backwards for opening tag
	while search_pos > 0:
		var char = html[search_pos]
		if char == '>':
			depth += 1
		elif char == '<':
			depth -= 1
			if depth == 0:
				return search_pos
		search_pos -= 1

	return max(0, pos - 1000)  # Fallback: 1000 chars before

func find_element_end(html: String, start_pos: int) -> int:
	"""Find the end of the HTML element"""
	var search_pos = start_pos
	var depth = 0

	while search_pos < html.length():
		var char = html[search_pos]
		if char == '<':
			depth += 1
		elif char == '>':
			depth -= 1
			if depth == 0:
				return search_pos + 1
		search_pos += 1

	return min(html.length(), start_pos + 2000)  # Fallback: 2000 chars after

func extract_title(element_html: String) -> String:
	"""Extract title from HTML element"""
	var title_patterns = [
		'<h1[^>]*>([^<]+)</h1>',
		'<h2[^>]*>([^<]+)</h2>',
		'<h3[^>]*>([^<]+)</h3>',
		'class="title"[^>]*>([^<]+)<',
		'class="post_title"[^>]*>([^<]+)<',
		'<a[^>]*title="([^"]+)"'
	]

	for pattern in title_patterns:
		var regex = RegEx.new()
		regex.compile(pattern)
		var result = regex.search(element_html)
		if result:
			return clean_html_text(result.get_string(1))

	return ""

func extract_content_preview(element_html: String) -> String:
	"""Extract content preview from HTML element"""
	var content_patterns = [
		'<p[^>]*>([^<]+)</p>',
		'class="content"[^>]*>([^<]+)<',
		'class="description"[^>]*>([^<]+)<',
		'class="excerpt"[^>]*>([^<]+)<'
	]

	for pattern in content_patterns:
		var regex = RegEx.new()
		regex.compile(pattern)
		var result = regex.search(element_html)
		if result:
			var content = clean_html_text(result.get_string(1))
			return content.substr(0, min(200, content.length())) + ("..." if content.length() > 200 else "")

	return "New post available"

func extract_post_url(element_html: String) -> String:
	"""Extract post URL from HTML element"""
	var url_patterns = [
		'<a[^>]*href="([^"]+)"',
		'data-url="([^"]+)"',
		'data-link="([^"]+)"'
	]

	for pattern in url_patterns:
		var regex = RegEx.new()
		regex.compile(pattern)
		var result = regex.search(element_html)
		if result:
			var url = result.get_string(1)
			# Make sure URL is absolute
			if url.begins_with("/"):
				url = "https://itch.io" + url
			elif not url.begins_with("http"):
				url = ITCH_IO_BASE_URL + "/" + url
			return url

	return ""

func clean_html_text(text: String) -> String:
	"""Clean HTML text by removing tags and decoding entities"""
	# Remove HTML tags
	var regex = RegEx.new()
	regex.compile("<[^>]*>")
	text = regex.sub(text, "", true)

	# Decode common HTML entities
	text = text.replace("&amp;", "&")
	text = text.replace("&lt;", "<")
	text = text.replace("&gt;", ">")
	text = text.replace("&quot;", "\"")
	text = text.replace("&#39;", "'")
	text = text.replace("&nbsp;", " ")

	# Clean up whitespace
	text = text.strip_edges()
	regex.compile("\\s+")
	text = regex.sub(text, " ", true)

	return text

func generate_post_id(title: String, url: String) -> String:
	"""Generate unique ID for a post"""
	var combined = title + "|" + url
	return str(combined.hash())

func is_known_post(post_id: String) -> bool:
	"""Check if we've already seen this post"""
	return known_posts.has(post_id)

# ============================================================================
# NOTIFICATION MANAGEMENT
# ============================================================================

func add_notification(post_data: Dictionary):
	"""Add a new notification"""
	notifications.append(post_data)
	known_posts[post_data.id] = true

	# Limit number of notifications
	while notifications.size() > MAX_NOTIFICATIONS:
		var removed = notifications.pop_front()
		known_posts.erase(removed.id)

	# Emit signal
	new_notification.emit(post_data.title, post_data.content, post_data.url)

	print("New notification: ", post_data.title)

func mark_notification_read(notification_id: String):
	"""Mark a notification as read"""
	for notification in notifications:
		if notification.id == notification_id:
			notification.read = true
			break
	save_notifications()
	update_button_text()

func mark_all_read():
	"""Mark all notifications as read"""
	for notification in notifications:
		notification.read = true
	save_notifications()
	update_button_text()

func get_unread_count() -> int:
	"""Get count of unread notifications"""
	var count = 0
	for notification in notifications:
		if not notification.read:
			count += 1
	return count

func clear_all_notifications():
	"""Clear all notifications"""
	notifications.clear()
	known_posts.clear()
	save_notifications()
	update_button_text()

# ============================================================================
# SAVE/LOAD SYSTEM
# ============================================================================

func save_notifications():
	"""Save notifications to file"""
	var save_data = {
		"notifications": notifications,
		"known_posts": known_posts,
		"last_check": Time.get_datetime_string_from_system()
	}

	var file = FileAccess.open(NOTIFICATIONS_FILE, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(save_data)
		file.store_string(json_string)
		file.close()

func load_notifications():
	"""Load notifications from file"""
	if FileAccess.file_exists(NOTIFICATIONS_FILE):
		var file = FileAccess.open(NOTIFICATIONS_FILE, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()

			var json = JSON.new()
			var parse_result = json.parse(json_string)

			if parse_result == OK:
				var data = json.data
				if data.has("notifications"):
					notifications = data.notifications
				if data.has("known_posts"):
					known_posts = data.known_posts

# ============================================================================
# UI SYSTEM
# ============================================================================

func update_button_text():
	"""Update button text with notification count"""
	var unread_count = get_unread_count()
	if unread_count > 0:
		text = "Notifications (" + str(unread_count) + ")"
	else:
		text = "Notifications"

func open_notification_window():
	"""Open the notification window"""
	if notification_window:
		notification_window.queue_free()

	# Create window
	notification_window = Window.new()
	notification_window.title = "Itch.io Notifications"
	notification_window.size = Vector2i(600, 400)
	notification_window.position = Vector2i(200, 200)
	notification_window.close_requested.connect(close_notification_window)

	# Add to scene tree
	get_tree().root.add_child(notification_window)

	# Create UI
	create_notification_ui()

	# Show window
	notification_window.show()

func close_notification_window():
	"""Close the notification window"""
	if notification_window:
		notification_window.queue_free()
		notification_window = null

func create_notification_ui():
	"""Create the notification window UI"""
	var main_container = VBoxContainer.new()
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 10)
	notification_window.add_child(main_container)

	# Header
	var header_container = HBoxContainer.new()

	var title_label = Label.new()
	title_label.text = "Itch.io Development Updates"
	title_label.add_theme_font_size_override("font_size", 18)
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	header_container.add_child(title_label)

	# Control buttons
	var refresh_button = Button.new()
	refresh_button.text = "Refresh"
	refresh_button.pressed.connect(_check_for_updates)
	header_container.add_child(refresh_button)

	var mark_all_button = Button.new()
	mark_all_button.text = "Mark All Read"
	mark_all_button.pressed.connect(mark_all_read)
	header_container.add_child(mark_all_button)

	var clear_button = Button.new()
	clear_button.text = "Clear All"
	clear_button.pressed.connect(clear_all_notifications)
	header_container.add_child(clear_button)

	main_container.add_child(header_container)

	# Status
	var status_container = HBoxContainer.new()

	notification_count_label = Label.new()
	update_notification_count_label()
	status_container.add_child(notification_count_label)

	var monitoring_label = Label.new()
	monitoring_label.text = "Monitoring: " + ("ON" if is_monitoring else "OFF")
	status_container.add_child(monitoring_label)

	var toggle_button = Button.new()
	toggle_button.text = "Stop" if is_monitoring else "Start"
	toggle_button.pressed.connect(toggle_monitoring)
	status_container.add_child(toggle_button)

	main_container.add_child(status_container)

	# Notification list
	var scroll_container = ScrollContainer.new()
	scroll_container.size_flags_vertical = Control.SIZE_EXPAND_FILL

	notification_list = VBoxContainer.new()
	notification_list.add_theme_constant_override("separation", 5)
	scroll_container.add_child(notification_list)
	main_container.add_child(scroll_container)

	# Populate notifications
	refresh_notification_list()

func refresh_notification_list():
	"""Refresh the notification list display"""
	if not notification_list:
		return

	# Clear existing items
	for child in notification_list.get_children():
		child.queue_free()

	# Add notifications (newest first)
	var sorted_notifications = notifications.duplicate()
	sorted_notifications.reverse()

	for notification in sorted_notifications:
		var item = create_notification_item(notification)
		notification_list.add_child(item)

	update_notification_count_label()

func create_notification_item(notification: Dictionary) -> Control:
	"""Create a single notification item"""
	var container = PanelContainer.new()
	container.custom_minimum_size = Vector2(0, 80)

	# Style based on read status
	if not notification.read:
		var style_box = StyleBoxFlat.new()
		style_box.bg_color = Color(0.3, 0.5, 0.8, 0.3)  # Light blue for unread
		container.add_theme_stylebox_override("panel", style_box)

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 5)
	container.add_child(vbox)

	# Header with title and type
	var header_hbox = HBoxContainer.new()

	var title_label = Label.new()
	title_label.text = notification.title
	title_label.add_theme_font_size_override("font_size", 14)
	title_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	title_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	header_hbox.add_child(title_label)

	var type_label = Label.new()
	type_label.text = "[" + notification.type.to_upper() + "]"
	type_label.add_theme_color_override("font_color", Color.CYAN)
	header_hbox.add_child(type_label)

	vbox.add_child(header_hbox)

	# Content
	if not notification.content.is_empty():
		var content_label = Label.new()
		content_label.text = notification.content
		content_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		content_label.add_theme_font_size_override("font_size", 12)
		vbox.add_child(content_label)

	# Footer with timestamp and actions
	var footer_hbox = HBoxContainer.new()

	var time_label = Label.new()
	time_label.text = notification.timestamp
	time_label.add_theme_font_size_override("font_size", 10)
	time_label.add_theme_color_override("font_color", Color.GRAY)
	time_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	footer_hbox.add_child(time_label)

	# Action buttons
	var open_button = Button.new()
	open_button.text = "Open"
	open_button.pressed.connect(_on_open_notification.bind(notification))
	footer_hbox.add_child(open_button)

	if not notification.read:
		var read_button = Button.new()
		read_button.text = "Mark Read"
		read_button.pressed.connect(_on_mark_read.bind(notification.id))
		footer_hbox.add_child(read_button)

	vbox.add_child(footer_hbox)

	return container

func update_notification_count_label():
	"""Update the notification count label"""
	if notification_count_label:
		var total = notifications.size()
		var unread = get_unread_count()
		notification_count_label.text = "Total: %d | Unread: %d" % [total, unread]

func toggle_monitoring():
	"""Toggle monitoring on/off"""
	if is_monitoring:
		stop_monitoring()
	else:
		start_monitoring()

	# Refresh UI if window is open
	if notification_window and notification_window.visible:
		close_notification_window()
		open_notification_window()

# ============================================================================
# EVENT HANDLERS
# ============================================================================

func _on_open_notification(notification: Dictionary):
	"""Handle opening a notification URL"""
	notification_clicked.emit(notification.url)

	# Mark as read when opened
	if not notification.read:
		mark_notification_read(notification.id)
		refresh_notification_list()

	# Open URL in browser
	OS.shell_open(notification.url)
	print("Opening URL: ", notification.url)

func _on_mark_read(notification_id: String):
	"""Handle marking notification as read"""
	mark_notification_read(notification_id)
	refresh_notification_list()

# ============================================================================
# PUBLIC API FUNCTIONS
# ============================================================================

func get_notification_summary() -> String:
	"""Get a summary of notifications"""
	var total = notifications.size()
	var unread = get_unread_count()
	var summary = "Notifications: %d total, %d unread\n" % [total, unread]
	summary += "Monitoring: %s\n" % ("Active" if is_monitoring else "Inactive")
	summary += "Check interval: %d seconds\n" % CHECK_INTERVAL
	summary += "Monitoring URL: %s" % ITCH_IO_BASE_URL
	return summary

func force_check():
	"""Force an immediate check for updates"""
	_check_for_updates()

func set_check_interval(seconds: float):
	"""Set the check interval"""
	check_timer.wait_time = seconds
	print("Check interval set to ", seconds, " seconds")

func get_notifications_by_type(type: String) -> Array:
	"""Get notifications filtered by type"""
	var filtered = []
	for notification in notifications:
		if notification.type == type:
			filtered.append(notification)
	return filtered

func export_notifications() -> Dictionary:
	"""Export all notifications data"""
	return {
		"notifications": notifications,
		"known_posts": known_posts,
		"settings": {
			"check_interval": CHECK_INTERVAL,
			"base_url": ITCH_IO_BASE_URL,
			"specific_game_url": SPECIFIC_GAME_URL
		}
	}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

func _exit_tree():
	"""Clean up when node is removed"""
	stop_monitoring()
	save_notifications()

func _notification(what):
	"""Handle engine notifications"""
	if what == NOTIFICATION_WM_CLOSE_REQUEST:
		save_notifications()
