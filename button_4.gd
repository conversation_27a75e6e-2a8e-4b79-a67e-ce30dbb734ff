extends Button

# Settings System - All-in-one implementation
# This file contains a complete settings system with UI, save/load functionality

# Settings data structure
var settings_data = {
	"graphics": {
		"resolution": "1920x1080",
		"fullscreen": false,
		"vsync": true,
		"quality": "High"
	},
	"audio": {
		"master_volume": 100,
		"music_volume": 80,
		"sfx_volume": 90,
		"muted": false
	},
	"gameplay": {
		"difficulty": "Normal",
		"auto_save": true,
		"show_fps": false,
		"mouse_sensitivity": 50
	},
	"controls": {
		"move_forward": "W",
		"move_backward": "S",
		"move_left": "A",
		"move_right": "D",
		"jump": "Space",
		"interact": "E"
	}
}

# Settings file path
const SETTINGS_FILE_PATH = "user://settings.json"

# UI References
var settings_window: Window
var settings_container: Control
var current_tab: String = "graphics"

# Signal for settings changes
signal settings_changed(category: String, key: String, value)

func _ready():
	# Connect button press to open settings
	connect("pressed", _on_button_pressed)

	# Load settings on startup
	load_settings()

	# Apply loaded settings
	apply_all_settings()

func _on_button_pressed():
	"""Called when the button is clicked - opens the settings window"""
	open_settings_window()

# ============================================================================
# SETTINGS SAVE/LOAD SYSTEM
# ============================================================================

func save_settings():
	"""Save current settings to file"""
	var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(settings_data)
		file.store_string(json_string)
		file.close()
		print("Settings saved successfully")
	else:
		print("Error: Could not save settings file")

func load_settings():
	"""Load settings from file, use defaults if file doesn't exist"""
	if FileAccess.file_exists(SETTINGS_FILE_PATH):
		var file = FileAccess.open(SETTINGS_FILE_PATH, FileAccess.READ)
		if file:
			var json_string = file.get_as_text()
			file.close()

			var json = JSON.new()
			var parse_result = json.parse(json_string)

			if parse_result == OK:
				var loaded_data = json.data
				# Merge loaded data with defaults to ensure all keys exist
				merge_settings_data(loaded_data)
				print("Settings loaded successfully")
			else:
				print("Error parsing settings file, using defaults")
		else:
			print("Error reading settings file, using defaults")
	else:
		print("Settings file not found, using defaults")

func merge_settings_data(loaded_data: Dictionary):
	"""Merge loaded settings with defaults to ensure all keys exist"""
	for category in settings_data.keys():
		if loaded_data.has(category) and loaded_data[category] is Dictionary:
			for key in settings_data[category].keys():
				if loaded_data[category].has(key):
					settings_data[category][key] = loaded_data[category][key]

# ============================================================================
# SETTINGS APPLICATION SYSTEM
# ============================================================================

func apply_all_settings():
	"""Apply all current settings to the game"""
	apply_graphics_settings()
	apply_audio_settings()
	apply_gameplay_settings()
	apply_controls_settings()

func apply_graphics_settings():
	"""Apply graphics settings"""
	var graphics = settings_data.graphics

	# Resolution
	if graphics.resolution != "":
		var res_parts = graphics.resolution.split("x")
		if res_parts.size() == 2:
			var width = res_parts[0].to_int()
			var height = res_parts[1].to_int()
			if width > 0 and height > 0:
				get_window().size = Vector2i(width, height)

	# Fullscreen
	if graphics.fullscreen:
		get_window().mode = Window.MODE_FULLSCREEN
	else:
		get_window().mode = Window.MODE_WINDOWED

	# VSync
	if graphics.vsync:
		DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_ENABLED)
	else:
		DisplayServer.window_set_vsync_mode(DisplayServer.VSYNC_DISABLED)

	print("Graphics settings applied")

func apply_audio_settings():
	"""Apply audio settings"""
	var audio = settings_data.audio

	# Set audio bus volumes (assuming standard audio buses exist)
	var master_bus = AudioServer.get_bus_index("Master")
	var music_bus = AudioServer.get_bus_index("Music")
	var sfx_bus = AudioServer.get_bus_index("SFX")

	if master_bus != -1:
		var master_db = linear_to_db(audio.master_volume / 100.0)
		AudioServer.set_bus_volume_db(master_bus, master_db)
		AudioServer.set_bus_mute(master_bus, audio.muted)

	if music_bus != -1:
		var music_db = linear_to_db(audio.music_volume / 100.0)
		AudioServer.set_bus_volume_db(music_bus, music_db)

	if sfx_bus != -1:
		var sfx_db = linear_to_db(audio.sfx_volume / 100.0)
		AudioServer.set_bus_volume_db(sfx_bus, sfx_db)

	print("Audio settings applied")

func apply_gameplay_settings():
	"""Apply gameplay settings"""
	var gameplay = settings_data.gameplay

	# These would typically affect game-specific systems
	# For now, we'll just store them and emit signals
	settings_changed.emit("gameplay", "difficulty", gameplay.difficulty)
	settings_changed.emit("gameplay", "auto_save", gameplay.auto_save)
	settings_changed.emit("gameplay", "show_fps", gameplay.show_fps)
	settings_changed.emit("gameplay", "mouse_sensitivity", gameplay.mouse_sensitivity)

	print("Gameplay settings applied")

func apply_controls_settings():
	"""Apply control settings"""
	# This would typically involve remapping input actions
	# For demonstration, we'll just emit signals
	for action in settings_data.controls.keys():
		var key = settings_data.controls[action]
		settings_changed.emit("controls", action, key)

	print("Controls settings applied")

# ============================================================================
# SETTINGS UI SYSTEM
# ============================================================================

func open_settings_window():
	"""Create and open the settings window"""
	if settings_window:
		settings_window.queue_free()

	# Create main window
	settings_window = Window.new()
	settings_window.title = "Game Settings"
	settings_window.size = Vector2i(800, 600)
	settings_window.position = Vector2i(100, 100)
	settings_window.close_requested.connect(_on_settings_window_closed)

	# Add to scene tree
	get_tree().root.add_child(settings_window)

	# Create main container
	settings_container = create_settings_ui()
	settings_window.add_child(settings_container)

	# Show window
	settings_window.show()

func _on_settings_window_closed():
	"""Handle settings window close"""
	if settings_window:
		settings_window.queue_free()
		settings_window = null

func create_settings_ui() -> Control:
	"""Create the complete settings UI"""
	var main_container = VBoxContainer.new()
	main_container.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	main_container.add_theme_constant_override("separation", 10)

	# Title
	var title = Label.new()
	title.text = "Game Settings"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title.add_theme_font_size_override("font_size", 24)
	main_container.add_child(title)

	# Tab container
	var tab_container = TabContainer.new()
	tab_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	main_container.add_child(tab_container)

	# Create tabs
	tab_container.add_child(create_graphics_tab())
	tab_container.add_child(create_audio_tab())
	tab_container.add_child(create_gameplay_tab())
	tab_container.add_child(create_controls_tab())

	# Bottom buttons
	var button_container = HBoxContainer.new()
	button_container.alignment = BoxContainer.ALIGNMENT_CENTER
	button_container.add_theme_constant_override("separation", 20)

	var save_button = Button.new()
	save_button.text = "Save & Apply"
	save_button.pressed.connect(_on_save_settings_pressed)
	button_container.add_child(save_button)

	var cancel_button = Button.new()
	cancel_button.text = "Cancel"
	cancel_button.pressed.connect(_on_cancel_settings_pressed)
	button_container.add_child(cancel_button)

	var reset_button = Button.new()
	reset_button.text = "Reset to Defaults"
	reset_button.pressed.connect(_on_reset_settings_pressed)
	button_container.add_child(reset_button)

	main_container.add_child(button_container)

	return main_container

# ============================================================================
# SETTINGS TAB CREATION
# ============================================================================

func create_graphics_tab() -> Control:
	"""Create graphics settings tab"""
	var container = ScrollContainer.new()
	container.name = "Graphics"

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 15)
	container.add_child(vbox)

	# Resolution setting
	var res_group = create_setting_group("Resolution")
	var res_option = OptionButton.new()
	res_option.add_item("1920x1080")
	res_option.add_item("1680x1050")
	res_option.add_item("1600x900")
	res_option.add_item("1366x768")
	res_option.add_item("1280x720")

	# Set current resolution
	var current_res = settings_data.graphics.resolution
	for i in res_option.get_item_count():
		if res_option.get_item_text(i) == current_res:
			res_option.selected = i
			break

	res_option.item_selected.connect(func(index):
		settings_data.graphics.resolution = res_option.get_item_text(index))
	res_group.add_child(res_option)
	vbox.add_child(res_group)

	# Fullscreen setting
	var fullscreen_group = create_setting_group("Display Mode")
	var fullscreen_check = CheckBox.new()
	fullscreen_check.text = "Fullscreen"
	fullscreen_check.button_pressed = settings_data.graphics.fullscreen
	fullscreen_check.toggled.connect(func(pressed):
		settings_data.graphics.fullscreen = pressed)
	fullscreen_group.add_child(fullscreen_check)
	vbox.add_child(fullscreen_group)

	# VSync setting
	var vsync_group = create_setting_group("VSync")
	var vsync_check = CheckBox.new()
	vsync_check.text = "Enable VSync"
	vsync_check.button_pressed = settings_data.graphics.vsync
	vsync_check.toggled.connect(func(pressed):
		settings_data.graphics.vsync = pressed)
	vsync_group.add_child(vsync_check)
	vbox.add_child(vsync_group)

	# Quality setting
	var quality_group = create_setting_group("Graphics Quality")
	var quality_option = OptionButton.new()
	quality_option.add_item("Low")
	quality_option.add_item("Medium")
	quality_option.add_item("High")
	quality_option.add_item("Ultra")

	# Set current quality
	var qualities = ["Low", "Medium", "High", "Ultra"]
	var current_quality = settings_data.graphics.quality
	for i in qualities.size():
		if qualities[i] == current_quality:
			quality_option.selected = i
			break

	quality_option.item_selected.connect(func(index):
		settings_data.graphics.quality = quality_option.get_item_text(index))
	quality_group.add_child(quality_option)
	vbox.add_child(quality_group)

	return container

func create_audio_tab() -> Control:
	"""Create audio settings tab"""
	var container = ScrollContainer.new()
	container.name = "Audio"

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 15)
	container.add_child(vbox)

	# Master volume
	var master_group = create_setting_group("Master Volume")
	var master_slider_container = create_volume_slider(settings_data.audio.master_volume)
	var master_slider = master_slider_container.get_child(0) as HSlider
	master_slider.value_changed.connect(func(value):
		settings_data.audio.master_volume = int(value))
	master_group.add_child(master_slider_container)
	vbox.add_child(master_group)

	# Music volume
	var music_group = create_setting_group("Music Volume")
	var music_slider_container = create_volume_slider(settings_data.audio.music_volume)
	var music_slider = music_slider_container.get_child(0) as HSlider
	music_slider.value_changed.connect(func(value):
		settings_data.audio.music_volume = int(value))
	music_group.add_child(music_slider_container)
	vbox.add_child(music_group)

	# SFX volume
	var sfx_group = create_setting_group("Sound Effects Volume")
	var sfx_slider_container = create_volume_slider(settings_data.audio.sfx_volume)
	var sfx_slider = sfx_slider_container.get_child(0) as HSlider
	sfx_slider.value_changed.connect(func(value):
		settings_data.audio.sfx_volume = int(value))
	sfx_group.add_child(sfx_slider_container)
	vbox.add_child(sfx_group)

	# Mute setting
	var mute_group = create_setting_group("Audio Options")
	var mute_check = CheckBox.new()
	mute_check.text = "Mute All Audio"
	mute_check.button_pressed = settings_data.audio.muted
	mute_check.toggled.connect(func(pressed):
		settings_data.audio.muted = pressed)
	mute_group.add_child(mute_check)
	vbox.add_child(mute_group)

	return container

func create_gameplay_tab() -> Control:
	"""Create gameplay settings tab"""
	var container = ScrollContainer.new()
	container.name = "Gameplay"

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 15)
	container.add_child(vbox)

	# Difficulty setting
	var difficulty_group = create_setting_group("Difficulty")
	var difficulty_option = OptionButton.new()
	difficulty_option.add_item("Easy")
	difficulty_option.add_item("Normal")
	difficulty_option.add_item("Hard")
	difficulty_option.add_item("Expert")

	var difficulties = ["Easy", "Normal", "Hard", "Expert"]
	var current_difficulty = settings_data.gameplay.difficulty
	for i in difficulties.size():
		if difficulties[i] == current_difficulty:
			difficulty_option.selected = i
			break

	difficulty_option.item_selected.connect(func(index):
		settings_data.gameplay.difficulty = difficulty_option.get_item_text(index))
	difficulty_group.add_child(difficulty_option)
	vbox.add_child(difficulty_group)

	# Auto-save setting
	var autosave_group = create_setting_group("Save Options")
	var autosave_check = CheckBox.new()
	autosave_check.text = "Enable Auto-Save"
	autosave_check.button_pressed = settings_data.gameplay.auto_save
	autosave_check.toggled.connect(func(pressed):
		settings_data.gameplay.auto_save = pressed)
	autosave_group.add_child(autosave_check)
	vbox.add_child(autosave_group)

	# Show FPS setting
	var fps_group = create_setting_group("Display Options")
	var fps_check = CheckBox.new()
	fps_check.text = "Show FPS Counter"
	fps_check.button_pressed = settings_data.gameplay.show_fps
	fps_check.toggled.connect(func(pressed):
		settings_data.gameplay.show_fps = pressed)
	fps_group.add_child(fps_check)
	vbox.add_child(fps_group)

	# Mouse sensitivity
	var sensitivity_group = create_setting_group("Mouse Sensitivity")
	var sensitivity_slider = HSlider.new()
	sensitivity_slider.min_value = 1
	sensitivity_slider.max_value = 100
	sensitivity_slider.step = 1
	sensitivity_slider.value = settings_data.gameplay.mouse_sensitivity
	sensitivity_slider.size_flags_horizontal = Control.SIZE_EXPAND_FILL

	var sensitivity_label = Label.new()
	sensitivity_label.text = str(settings_data.gameplay.mouse_sensitivity)

	sensitivity_slider.value_changed.connect(func(value):
		settings_data.gameplay.mouse_sensitivity = int(value)
		sensitivity_label.text = str(int(value)))

	var sensitivity_hbox = HBoxContainer.new()
	sensitivity_hbox.add_child(sensitivity_slider)
	sensitivity_hbox.add_child(sensitivity_label)
	sensitivity_group.add_child(sensitivity_hbox)
	vbox.add_child(sensitivity_group)

	return container

func create_controls_tab() -> Control:
	"""Create controls settings tab"""
	var container = ScrollContainer.new()
	container.name = "Controls"

	var vbox = VBoxContainer.new()
	vbox.add_theme_constant_override("separation", 15)
	container.add_child(vbox)

	# Create control mapping UI
	var controls = settings_data.controls
	for action in controls.keys():
		var control_group = create_setting_group(action.capitalize().replace("_", " "))

		var hbox = HBoxContainer.new()
		var key_button = Button.new()
		key_button.text = str(controls[action])
		key_button.custom_minimum_size = Vector2(100, 30)

		# Store action name in button metadata for the callback
		key_button.set_meta("action", action)
		key_button.pressed.connect(_on_key_button_pressed.bind(key_button))

		var reset_key_button = Button.new()
		reset_key_button.text = "Reset"
		reset_key_button.pressed.connect(func():
			# Reset to default - you'd need to store defaults separately
			pass)

		hbox.add_child(key_button)
		hbox.add_child(reset_key_button)
		control_group.add_child(hbox)
		vbox.add_child(control_group)

	return container

# ============================================================================
# UI HELPER FUNCTIONS
# ============================================================================

func create_setting_group(title: String) -> VBoxContainer:
	"""Create a labeled group container for settings"""
	var group = VBoxContainer.new()
	group.add_theme_constant_override("separation", 5)

	var label = Label.new()
	label.text = title
	label.add_theme_font_size_override("font_size", 16)
	group.add_child(label)

	return group

func create_volume_slider(current_value: int) -> HBoxContainer:
	"""Create a volume slider with label"""
	var hbox = HBoxContainer.new()

	var slider = HSlider.new()
	slider.min_value = 0
	slider.max_value = 100
	slider.step = 1
	slider.value = current_value
	slider.size_flags_horizontal = Control.SIZE_EXPAND_FILL

	var label = Label.new()
	label.text = str(current_value) + "%"
	label.custom_minimum_size = Vector2(50, 0)

	slider.value_changed.connect(func(value):
		label.text = str(int(value)) + "%")

	hbox.add_child(slider)
	hbox.add_child(label)

	return hbox

func _on_key_button_pressed(button: Button):
	"""Handle key remapping button press"""
	var action = button.get_meta("action")
	button.text = "Press any key..."
	button.disabled = true

	# In a real implementation, you'd capture the next key press
	# For now, we'll just simulate it
	await get_tree().create_timer(1.0).timeout

	# Simulate key capture (in real implementation, use Input.is_action_just_pressed)
	var new_key = "F" # Placeholder
	settings_data.controls[action] = new_key
	button.text = new_key
	button.disabled = false

# ============================================================================
# SETTINGS BUTTON HANDLERS
# ============================================================================

func _on_save_settings_pressed():
	"""Handle save settings button press"""
	save_settings()
	apply_all_settings()

	if settings_window:
		settings_window.queue_free()
		settings_window = null

	print("Settings saved and applied!")

func _on_cancel_settings_pressed():
	"""Handle cancel settings button press"""
	# Reload settings to discard changes
	load_settings()

	if settings_window:
		settings_window.queue_free()
		settings_window = null

	print("Settings changes cancelled")

func _on_reset_settings_pressed():
	"""Handle reset to defaults button press"""
	# Reset to default values
	settings_data = {
		"graphics": {
			"resolution": "1920x1080",
			"fullscreen": false,
			"vsync": true,
			"quality": "High"
		},
		"audio": {
			"master_volume": 100,
			"music_volume": 80,
			"sfx_volume": 90,
			"muted": false
		},
		"gameplay": {
			"difficulty": "Normal",
			"auto_save": true,
			"show_fps": false,
			"mouse_sensitivity": 50
		},
		"controls": {
			"move_forward": "W",
			"move_backward": "S",
			"move_left": "A",
			"move_right": "D",
			"jump": "Space",
			"interact": "E"
		}
	}

	# Recreate the UI to reflect the reset values
	if settings_window:
		settings_window.queue_free()
		settings_window = null

	# Reopen settings window with reset values
	open_settings_window()

	print("Settings reset to defaults")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

func get_setting(category: String, key: String):
	"""Get a specific setting value"""
	if settings_data.has(category) and settings_data[category].has(key):
		return settings_data[category][key]
	return null

func set_setting(category: String, key: String, value):
	"""Set a specific setting value"""
	if settings_data.has(category):
		settings_data[category][key] = value
		settings_changed.emit(category, key, value)

func reset_category(category: String):
	"""Reset a specific category to defaults"""
	match category:
		"graphics":
			settings_data.graphics = {
				"resolution": "1920x1080",
				"fullscreen": false,
				"vsync": true,
				"quality": "High"
			}
		"audio":
			settings_data.audio = {
				"master_volume": 100,
				"music_volume": 80,
				"sfx_volume": 90,
				"muted": false
			}
		"gameplay":
			settings_data.gameplay = {
				"difficulty": "Normal",
				"auto_save": true,
				"show_fps": false,
				"mouse_sensitivity": 50
			}
		"controls":
			settings_data.controls = {
				"move_forward": "W",
				"move_backward": "S",
				"move_left": "A",
				"move_right": "D",
				"jump": "Space",
				"interact": "E"
			}

# ============================================================================
# PUBLIC API FUNCTIONS
# ============================================================================

func export_settings() -> Dictionary:
	"""Export current settings as dictionary"""
	return settings_data.duplicate(true)

func import_settings(new_settings: Dictionary):
	"""Import settings from dictionary"""
	merge_settings_data(new_settings)
	apply_all_settings()

func get_settings_summary() -> String:
	"""Get a text summary of current settings"""
	var summary = "Current Settings:\n"
	summary += "Graphics: %s, %s, VSync: %s, Quality: %s\n" % [
		settings_data.graphics.resolution,
		"Fullscreen" if settings_data.graphics.fullscreen else "Windowed",
		"On" if settings_data.graphics.vsync else "Off",
		settings_data.graphics.quality
	]
	summary += "Audio: Master: %d%%, Music: %d%%, SFX: %d%%, Muted: %s\n" % [
		settings_data.audio.master_volume,
		settings_data.audio.music_volume,
		settings_data.audio.sfx_volume,
		"Yes" if settings_data.audio.muted else "No"
	]
	summary += "Gameplay: %s difficulty, Auto-save: %s, FPS: %s, Sensitivity: %d\n" % [
		settings_data.gameplay.difficulty,
		"On" if settings_data.gameplay.auto_save else "Off",
		"On" if settings_data.gameplay.show_fps else "Off",
		settings_data.gameplay.mouse_sensitivity
	]
	return summary
